-- STEP-BY-STEP FIX FOR PAYMENTS TABLE
-- Run these commands ONE AT A TIME in your MySQL database
-- If a command fails, just move to the next one

-- ========================================
-- STEP 1: Show current table structure
-- ========================================
SHOW CREATE TABLE `payments`;

-- ========================================
-- STEP 2: Try to drop foreign key constraints
-- Run these one by one, ignore errors if constraint doesn't exist
-- ========================================

-- Try constraint name 1:
ALTER TABLE `payments` DROP FOREIGN KEY `payments_ibfk_1`;

-- Try constraint name 2:
ALTER TABLE `payments` DROP FOREIGN KEY `payments_ibfk_2`;

-- Try constraint name 3:
ALTER TABLE `payments` DROP FOREIGN KEY `fk_payments_customer`;

-- Try constraint name 4:
ALTER TABLE `payments` DROP FOREIGN KEY `fk_payments_customer_id`;

-- ========================================
-- STEP 3: Modify customer_id column
-- ========================================
ALTER TABLE `payments` MODIFY COLUMN `customer_id` INT(11) NULL;

-- ========================================
-- STEP 4: Add new foreign key constraint
-- ========================================
ALTER TABLE `payments` 
ADD CONSTRAINT `fk_payments_customer_new` 
FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- ========================================
-- STEP 5: Verify the fix
-- ========================================
DESCRIBE `payments`;

-- ========================================
-- STEP 6: Test with a sample insert (optional)
-- ========================================
-- Get a customer ID first:
-- SELECT id FROM customers LIMIT 1;

-- Then test insert (replace 123 with actual customer ID):
-- INSERT INTO payments (transaction_id, customer_id, amount, payment_method, status, payment_date, created_at, updated_at)
-- VALUES ('TEST-001', 123, 10.00, 'bank_transfer', 'pending', NOW(), NOW(), NOW());

-- Clean up test:
-- DELETE FROM payments WHERE transaction_id = 'TEST-001';
