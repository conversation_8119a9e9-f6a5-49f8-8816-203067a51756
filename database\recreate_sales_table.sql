-- Recreate sales table with correct foreign key constraints
-- BAC<PERSON>UP YOUR DATA FIRST if you have important sales records!

-- Step 1: Backup existing sales data (if any)
CREATE TABLE IF NOT EXISTS `sales_backup` AS SELECT * FROM `sales`;

-- Step 2: Drop the existing sales table
DROP TABLE IF EXISTS `sales`;

-- Step 3: Create new sales table with correct structure
CREATE TABLE `sales` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NULL,
  `sale_date` date NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` varchar(50) NOT NULL DEFAULT 'Cash',
  `payment_status` enum('Paid','Pending','Partial','Refunded') NOT NULL DEFAULT 'Paid',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_sale_date` (`sale_date`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_payment_status` (`payment_status`),
  CONSTRAINT `fk_sales_customer_correct` FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_sales_created_by_correct` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 4: Restore data from backup (if any existed)
INSERT INTO `sales` SELECT * FROM `sales_backup` WHERE EXISTS (SELECT 1 FROM `sales_backup`);

-- Step 5: Drop backup table (optional - comment out if you want to keep it)
-- DROP TABLE `sales_backup`;

-- Step 6: Verify the new structure
DESCRIBE `sales`;
SHOW CREATE TABLE `sales`;
