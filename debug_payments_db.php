<?php
/**
 * Debug script to check payments table structure and data
 * Run this script to diagnose payment insertion issues
 */

require_once 'database/conn.php';

echo "<h2>Database Diagnostics for Payments</h2>";

try {
    // Check if payments table exists
    echo "<h3>1. Checking if payments table exists...</h3>";
    $result = $pdo->query("SHOW TABLES LIKE 'payments'");
    if ($result->rowCount() > 0) {
        echo "✅ Payments table exists<br>";
    } else {
        echo "❌ Payments table does NOT exist<br>";
        echo "<strong>Solution:</strong> Run the database/create_payments_tables.sql script<br>";
        exit;
    }

    // Check payments table structure
    echo "<h3>2. Payments table structure:</h3>";
    $result = $pdo->query("DESCRIBE payments");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table><br>";

    // Check payment_method enum values
    echo "<h3>3. Payment method enum values:</h3>";
    $result = $pdo->query("SHOW COLUMNS FROM payments LIKE 'payment_method'");
    $column = $result->fetch();
    echo "Current enum values: <strong>{$column['Type']}</strong><br>";
    
    // Check if our payment methods are supported
    $our_methods = ['shilling_somali', 'evc', 'bank_transfer', 'cash_dollar'];
    echo "Our payment methods: " . implode(', ', $our_methods) . "<br>";
    
    foreach ($our_methods as $method) {
        if (strpos($column['Type'], $method) !== false) {
            echo "✅ $method is supported<br>";
        } else {
            echo "❌ $method is NOT supported<br>";
        }
    }

    // Check if customers table exists
    echo "<h3>4. Checking customers table...</h3>";
    $result = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($result->rowCount() > 0) {
        echo "✅ Customers table exists<br>";
        
        // Count customers
        $result = $pdo->query("SELECT COUNT(*) as count FROM customers");
        $count = $result->fetch()['count'];
        echo "Number of customers: $count<br>";
        
        if ($count > 0) {
            echo "Sample customers:<br>";
            $result = $pdo->query("SELECT id, customer_name, email, status FROM customers LIMIT 5");
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Status</th></tr>";
            while ($row = $result->fetch()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['customer_name']}</td>";
                echo "<td>{$row['email']}</td>";
                echo "<td>{$row['status']}</td>";
                echo "</tr>";
            }
            echo "</table><br>";
        }
    } else {
        echo "❌ Customers table does NOT exist<br>";
        echo "<strong>Solution:</strong> Create customers table first<br>";
    }

    // Check foreign key constraints
    echo "<h3>5. Foreign key constraints on payments table:</h3>";
    $result = $pdo->query("
        SELECT
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE
        WHERE TABLE_NAME = 'payments'
        AND TABLE_SCHEMA = 'meat_sys'
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");

    if ($result->rowCount() > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Constraint</th><th>Column</th><th>References Table</th><th>References Column</th></tr>";
        while ($row = $result->fetch()) {
            echo "<tr>";
            echo "<td>{$row['CONSTRAINT_NAME']}</td>";
            echo "<td>{$row['COLUMN_NAME']}</td>";
            echo "<td>{$row['REFERENCED_TABLE_NAME']}</td>";
            echo "<td>{$row['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";

            // Check if this is the problematic constraint
            if ($row['COLUMN_NAME'] == 'customer_id' && $row['REFERENCED_TABLE_NAME'] == 'users') {
                echo "<tr><td colspan='4' style='background-color: #ffcccc;'>";
                echo "⚠️ PROBLEM: customer_id references 'users' table but we want 'customers' table";
                echo "</td></tr>";
            }
        }
        echo "</table><br>";
    } else {
        echo "No foreign key constraints found<br>";
    }

    // Additional check: Show what customer IDs are being sent vs what exists
    echo "<h3>6. Customer ID validation:</h3>";
    if (isset($_POST['customer_id'])) {
        $sent_customer_id = $_POST['customer_id'];
        echo "Customer ID being sent: $sent_customer_id<br>";

        // Check if this ID exists in customers table
        $stmt = $pdo->prepare("SELECT id, customer_name FROM customers WHERE id = ?");
        $stmt->execute([$sent_customer_id]);
        if ($stmt->rowCount() > 0) {
            $customer = $stmt->fetch();
            echo "✅ Customer found: {$customer['customer_name']}<br>";
        } else {
            echo "❌ Customer ID $sent_customer_id NOT found in customers table<br>";
        }

        // Check if this ID exists in users table (old reference)
        $stmt = $pdo->prepare("SELECT id, name FROM users WHERE id = ?");
        $stmt->execute([$sent_customer_id]);
        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch();
            echo "Note: This ID exists in users table: {$user['name']}<br>";
        }
    }

    // Test payment insertion with sample data
    echo "<h3>6. Testing payment insertion...</h3>";
    
    // Get first customer ID
    $result = $pdo->query("SELECT id FROM customers WHERE status = 'Active' LIMIT 1");
    if ($result->rowCount() > 0) {
        $customer_id = $result->fetch()['id'];
        echo "Using customer ID: $customer_id<br>";
        
        // Try to insert a test payment
        try {
            $test_transaction_id = 'TEST-' . time();
            $stmt = $pdo->prepare("
                INSERT INTO payments (transaction_id, customer_id, amount, payment_method, status, payment_date, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, NOW(), NOW(), NOW())
            ");
            
            $result = $stmt->execute([
                $test_transaction_id,
                $customer_id,
                10.00,
                'bank_transfer', // Using a method that should exist
                'pending'
            ]);
            
            if ($result) {
                echo "✅ Test payment inserted successfully<br>";
                
                // Clean up test payment
                $pdo->prepare("DELETE FROM payments WHERE transaction_id = ?")->execute([$test_transaction_id]);
                echo "Test payment cleaned up<br>";
            } else {
                echo "❌ Test payment insertion failed<br>";
            }
            
        } catch (PDOException $e) {
            echo "❌ Test payment insertion error: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ No active customers found for testing<br>";
    }

} catch (PDOException $e) {
    echo "Database connection error: " . $e->getMessage();
}
?>

<style>
table { margin: 10px 0; }
th, td { padding: 5px 10px; text-align: left; }
th { background-color: #f0f0f0; }
</style>
