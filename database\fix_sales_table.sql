-- Fix sales table to work with customers table and correct payment methods
-- Run this SQL in your MySQL database to fix the sales table
-- This version avoids using information_schema due to permission issues

-- STEP-BY-STEP FIX FOR SALES TABLE
-- Run these commands ONE AT A TIME in your MySQL database
-- If a command fails, just move to the next one

-- ========================================
-- STEP 1: Show current table structure
-- ========================================
SHOW CREATE TABLE `sales`;

-- ========================================
-- STEP 2: Try to drop foreign key constraints
-- Run these one by one, ignore errors if constraint doesn't exist
-- ========================================

-- Try constraint name 1 (customer_id):
ALTER TABLE `sales` DROP FOREIGN KEY `sales_ibfk_1`;

-- Try constraint name 2 (created_by):
ALTER TABLE `sales` DROP FOREIGN KEY `sales_ibfk_2`;

-- Try other possible constraint names:
ALTER TABLE `sales` DROP FOREIGN KEY `fk_sales_customer`;
ALTER TABLE `sales` DROP FOREIGN KEY `fk_sales_created_by`;

-- ========================================
-- STEP 3: Modify columns to allow NULL and fix types
-- ========================================
ALTER TABLE `sales` MODIFY COLUMN `customer_id` INT(11) NULL;
ALTER TABLE `sales` MODIFY COLUMN `created_by` INT(11) NULL;

-- Update payment_method to include your preferred methods
ALTER TABLE `sales` MODIFY COLUMN `payment_method` 
VARCHAR(50) NOT NULL DEFAULT 'Cash';

-- ========================================
-- STEP 4: Add new foreign key constraints
-- ========================================
-- Add constraint to reference customers table instead of users
ALTER TABLE `sales` 
ADD CONSTRAINT `fk_sales_customer_new` 
FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Keep created_by referencing users table (for staff who create sales)
ALTER TABLE `sales` 
ADD CONSTRAINT `fk_sales_created_by_new` 
FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- ========================================
-- STEP 5: Verify the changes
-- ========================================
DESCRIBE `sales`;

-- ========================================
-- STEP 6: Test with a sample insert (optional)
-- ========================================
-- Get a customer ID first:
-- SELECT id FROM customers LIMIT 1;

-- Get a user ID for created_by:
-- SELECT id FROM users LIMIT 1;

-- Then test insert (replace 123 and 1 with actual IDs):
-- INSERT INTO sales (customer_id, sale_date, total_amount, payment_method, payment_status, notes, created_by, created_at, updated_at)
-- VALUES (123, CURDATE(), 50.00, 'Cash', 'Paid', 'Test sale', 1, NOW(), NOW());

-- Clean up test:
-- DELETE FROM sales WHERE notes = 'Test sale';
