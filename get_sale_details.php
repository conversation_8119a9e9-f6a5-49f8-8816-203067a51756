<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in
requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

try {
    if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
        throw new Exception('Invalid sale ID');
    }

    $sale_id = intval($_GET['id']);

    // Get sale details with customer information
    $sale_query = "
        SELECT s.*, u.customer_name, u.email as customer_email, u.phone as customer_phone,
               creator.name as created_by_name
        FROM sales s
        LEFT JOIN customers u ON s.customer_id = u.id
        LEFT JOIN users creator ON s.created_by = creator.id
        WHERE s.id = ?
    ";
    
    $sale_stmt = $pdo->prepare($sale_query);
    $sale_stmt->execute([$sale_id]);
    $sale = $sale_stmt->fetch();

    if (!$sale) {
        throw new Exception('Sale not found');
    }

    // Get sale items with product details
    $items_query = "
        SELECT si.*, p.product as product_name, p.category as product_category
        FROM sale_items si
        LEFT JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = ?
        ORDER BY si.id
    ";
    
    $items_stmt = $pdo->prepare($items_query);
    $items_stmt->execute([$sale_id]);
    $items = $items_stmt->fetchAll();

    // Format the response
    $response = [
        'success' => true,
        'sale' => [
            'id' => $sale['id'],
            'sale_date' => $sale['sale_date'],
            'total_amount' => floatval($sale['total_amount']),
            'payment_method' => $sale['payment_method'],
            'payment_status' => $sale['payment_status'],
            'notes' => $sale['notes'],
            'created_at' => $sale['created_at'],
            'updated_at' => $sale['updated_at'],
            'customer' => [
                'name' => $sale['customer_name'],
                'email' => $sale['customer_email'],
                'phone' => $sale['customer_phone']
            ],
            'created_by' => $sale['created_by_name']
        ],
        'items' => []
    ];

    foreach ($items as $item) {
        $response['items'][] = [
            'id' => $item['id'],
            'product_id' => $item['product_id'],
            'product_name' => $item['product_name'],
            'product_category' => $item['product_category'],
            'quantity' => intval($item['quantity']),
            'unit_price' => floatval($item['unit_price']),
            'subtotal' => floatval($item['subtotal'])
        ];
    }

    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
