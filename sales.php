<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in to access sales
requireLogin();

// Optional: Require specific role for sales management (uncomment if needed)
// requireRole('Staff'); // Staff and above can manage sales

// Handle AJAX requests for payment data
if (isset($_GET['action']) && $_GET['action'] === 'get_payment_info' && isset($_GET['payment_id'])) {
    header('Content-Type: application/json');

    try {
        $payment_id = intval($_GET['payment_id']);
        $stmt = $pdo->prepare("
            SELECT
                p.id as payment_id,
                p.transaction_id,
                p.customer_id,
                p.amount,
                p.payment_method,
                p.status,
                p.reference_number,
                u.customer_name as customer_name,
                u.email as customer_email,
                u.phone as customer_phone
            FROM payments p
            LEFT JOIN customers u ON p.customer_id = u.id
            WHERE p.id = ? AND p.status = 'completed'
        ");
        $stmt->execute([$payment_id]);
        $payment = $stmt->fetch();

        if ($payment) {
            echo json_encode(['success' => true, 'payment' => $payment]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Payment not found or not completed']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error fetching payment data']);
    }
    exit;
}

// Handle form submission for adding sales
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        if (isset($_POST['action']) && $_POST['action'] === 'add_sale') {
            // Handle add new sale
            $customer_id = intval($_POST['customer_id'] ?? 0);
            $sale_date = $_POST['sale_date'] ?? date('Y-m-d');
            $payment_method = trim($_POST['payment_method'] ?? '');
            $notes = trim($_POST['notes'] ?? '');
            $sale_items = $_POST['sale_items'] ?? [];

            // Server-side validation
            $errors = [];
            if ($customer_id <= 0) $errors[] = 'Customer is required';
            if (empty($payment_method)) $errors[] = 'Payment method is required';
            if (empty($sale_items)) $errors[] = 'At least one product must be selected';

            // Validate sale items
            $total_amount = 0;
            $valid_items = [];
            foreach ($sale_items as $item) {
                $product_id = intval($item['product_id'] ?? 0);
                $quantity = intval($item['quantity'] ?? 0);
                $unit_price = floatval($item['unit_price'] ?? 0);

                if ($product_id <= 0 || $quantity <= 0 || $unit_price <= 0) {
                    $errors[] = 'Invalid product details';
                    continue;
                }

                // Check if product exists and has enough stock
                $product_check = $pdo->prepare("SELECT product, stock, price FROM products WHERE id = ?");
                $product_check->execute([$product_id]);
                $product = $product_check->fetch();

                if (!$product) {
                    $errors[] = 'Product not found';
                    continue;
                }

                if ($product['stock'] < $quantity) {
                    $errors[] = "Insufficient stock for {$product['product']}. Available: {$product['stock']}, Requested: {$quantity}";
                    continue;
                }

                $subtotal = $quantity * $unit_price;
                $total_amount += $subtotal;

                $valid_items[] = [
                    'product_id' => $product_id,
                    'product_name' => $product['product'],
                    'quantity' => $quantity,
                    'unit_price' => $unit_price,
                    'subtotal' => $subtotal
                ];
            }

            if (!empty($errors)) {
                $message = implode(', ', $errors);
                $messageType = 'error';
            } else {
                // Begin transaction
                $pdo->beginTransaction();

                try {
                    // Insert sale record
                    $sale_stmt = $pdo->prepare("
                        INSERT INTO sales (customer_id, sale_date, total_amount, payment_method, notes, created_by, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
                    ");
                    $sale_result = $sale_stmt->execute([
                        $customer_id,
                        $sale_date,
                        $total_amount,
                        $payment_method,
                        $notes,
                        $_SESSION['user_id']
                    ]);

                    if (!$sale_result) {
                        throw new Exception('Failed to create sale record');
                    }

                    $sale_id = $pdo->lastInsertId();

                    // Insert sale items and update stock
                    foreach ($valid_items as $item) {
                        // Insert sale item
                        $item_stmt = $pdo->prepare("
                            INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, subtotal, created_at)
                            VALUES (?, ?, ?, ?, ?, NOW())
                        ");
                        $item_result = $item_stmt->execute([
                            $sale_id,
                            $item['product_id'],
                            $item['quantity'],
                            $item['unit_price'],
                            $item['subtotal']
                        ]);

                        if (!$item_result) {
                            throw new Exception('Failed to add sale item');
                        }

                        // Update product stock
                        $stock_stmt = $pdo->prepare("
                            UPDATE products
                            SET stock = stock - ?, updated_at = NOW()
                            WHERE id = ?
                        ");
                        $stock_result = $stock_stmt->execute([
                            $item['quantity'],
                            $item['product_id']
                        ]);

                        if (!$stock_result) {
                            throw new Exception('Failed to update product stock');
                        }

                        // Update product status based on new stock level
                        $new_stock_stmt = $pdo->prepare("SELECT stock FROM products WHERE id = ?");
                        $new_stock_stmt->execute([$item['product_id']]);
                        $new_stock = $new_stock_stmt->fetch()['stock'];

                        $new_status = 'In Stock';
                        if ($new_stock == 0) {
                            $new_status = 'Out of Stock';
                        } elseif ($new_stock <= 10) { // Low stock threshold
                            $new_status = 'Low Stock';
                        }

                        $status_stmt = $pdo->prepare("UPDATE products SET status = ? WHERE id = ?");
                        $status_stmt->execute([$new_status, $item['product_id']]);
                    }

                    // Commit transaction
                    $pdo->commit();

                    $message = "Sale #$sale_id created successfully! Total amount: $" . number_format($total_amount, 2);
                    $messageType = 'success';

                } catch (Exception $e) {
                    // Rollback transaction
                    $pdo->rollback();
                    throw $e;
                }
            }
        }

    } catch (PDOException $e) {
        // Log the actual error for debugging (don't show to user)
        error_log("Database error: " . $e->getMessage());
        $message = 'A database error occurred. Please try again later.';
        $messageType = 'error';
    } catch (Exception $e) {
        // Log any other errors
        error_log("General error: " . $e->getMessage());
        $message = $e->getMessage();
        $messageType = 'error';
    }
}
?>

<?php include("include/header.php") ?>
<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include("include/sidebar.php") ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <?php include("include/top_navbar.php") ?>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Sales Management</h1>
                        <p class="mt-1 text-sm text-gray-600">Create and manage sales transactions</p>
                    </div>
                    <div class="mt-4 md:mt-0 space-x-2">
                        <button onclick="openAddSaleModal()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i> New Sale
                        </button>
                        <button onclick="exportSales()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-download mr-2"></i> Export
                        </button>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <?php if (!empty($message)): ?>
                <div class="mb-6">
                    <div class="<?php echo $messageType == 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded relative" role="alert">
                        <strong class="font-bold"><?php echo $messageType == 'success' ? 'Success!' : 'Error!'; ?></strong>
                        <span class="block sm:inline"><?php echo htmlspecialchars($message); ?></span>
                        <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                            <svg class="fill-current h-6 w-6 text-<?php echo $messageType == 'success' ? 'green' : 'red'; ?>-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" onclick="this.parentElement.parentElement.style.display='none';">
                                <title>Close</title>
                                <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                            </svg>
                        </span>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Sales Stats -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <?php
                    // Get real sales statistics from database
                    try {
                        // Total sales today
                        $today_sales = $pdo->query("SELECT COALESCE(SUM(total_amount), 0) as total FROM sales WHERE DATE(sale_date) = CURDATE()")->fetch()['total'] ?? 0;

                        // Total sales this month
                        $month_sales = $pdo->query("SELECT COALESCE(SUM(total_amount), 0) as total FROM sales WHERE MONTH(sale_date) = MONTH(CURDATE()) AND YEAR(sale_date) = YEAR(CURDATE())")->fetch()['total'] ?? 0;

                        // Total sales count today
                        $today_count = $pdo->query("SELECT COUNT(*) as count FROM sales WHERE DATE(sale_date) = CURDATE()")->fetch()['count'] ?? 0;

                        // Total sales count this month
                        $month_count = $pdo->query("SELECT COUNT(*) as count FROM sales WHERE MONTH(sale_date) = MONTH(CURDATE()) AND YEAR(sale_date) = YEAR(CURDATE())")->fetch()['count'] ?? 0;

                        // Average order value this month
                        $avg_order = $month_count > 0 ? $month_sales / $month_count : 0;

                        // New customers this month
                        $new_customers = $pdo->query("SELECT COUNT(DISTINCT customer_id) as count FROM sales WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())")->fetch()['count'] ?? 0;

                    } catch (Exception $e) {
                        $today_sales = $month_sales = $today_count = $month_count = $avg_order = $new_customers = 0;
                    }
                    ?>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Today's Sales</p>
                                <h3 class="text-xl font-semibold">$<?php echo number_format($today_sales, 2); ?></h3>
                                <p class="text-xs text-gray-500"><?php echo $today_count; ?> transactions</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">This Month</p>
                                <h3 class="text-xl font-semibold">$<?php echo number_format($month_sales, 2); ?></h3>
                                <p class="text-xs text-gray-500"><?php echo $month_count; ?> sales</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Avg. Order Value</p>
                                <h3 class="text-xl font-semibold">$<?php echo number_format($avg_order, 2); ?></h3>
                                <p class="text-xs text-gray-500">This month</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Active Customers</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($new_customers); ?></h3>
                                <p class="text-xs text-gray-500">This month</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sales Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Recent Sales</h3>
                        <div class="flex items-center space-x-2">
                            <button onclick="printSales()" class="text-gray-500 hover:text-gray-700" title="Print Sales">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sale ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php
                                try {
                                    // Fetch recent sales from database
                                    $sales_query = "
                                        SELECT s.id, s.sale_date, s.total_amount, s.payment_method, s.payment_status, s.notes,
                                               u.customer_name as customer_name, u.email as customer_email,
                                               COUNT(si.id) as item_count
                                        FROM sales s
                                        LEFT JOIN customers u ON s.customer_id = u.id
                                        LEFT JOIN sale_items si ON s.id = si.sale_id
                                        GROUP BY s.id, s.sale_date, s.total_amount, s.payment_method, s.payment_status, s.notes, u.customer_name, u.email
                                        ORDER BY s.created_at DESC
                                        LIMIT 10
                                    ";
                                    $sales_result = $pdo->query($sales_query);
                                    $sales = $sales_result->fetchAll();

                                    if (count($sales) > 0) {
                                        foreach ($sales as $sale):
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">#<?php echo htmlspecialchars($sale['id']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo $sale['item_count']; ?> items</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($sale['customer_name'] ?? 'N/A'); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($sale['customer_email']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo date('M d, Y', strtotime($sale['sale_date'])); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">$<?php echo number_format($sale['total_amount'], 2); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($sale['payment_method']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_colors = [
                                            'Paid' => 'bg-green-100 text-green-800',
                                            'Pending' => 'bg-yellow-100 text-yellow-800',
                                            'Partial' => 'bg-blue-100 text-blue-800',
                                            'Refunded' => 'bg-red-100 text-red-800'
                                        ];
                                        $color_class = $status_colors[$sale['payment_status']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $color_class; ?>">
                                            <?php echo htmlspecialchars($sale['payment_status']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="#" onclick="viewSale(<?php echo $sale['id']; ?>)" class="text-blue-600 hover:text-blue-900 mr-3" title="View Sale">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" onclick="printReceipt(<?php echo $sale['id']; ?>)" class="text-green-600 hover:text-green-900" title="Print Receipt">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php
                                        endforeach;
                                    } else {
                                ?>
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-receipt text-4xl text-gray-300 mb-2"></i>
                                            <p class="text-lg font-medium">No sales found</p>
                                            <p class="text-sm">Click "New Sale" to create your first sale</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                    }
                                } catch (Exception $e) {
                                    error_log("Error fetching sales: " . $e->getMessage());
                                ?>
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-red-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-exclamation-triangle text-4xl text-red-300 mb-2"></i>
                                            <p class="text-lg font-medium">Error loading sales</p>
                                            <p class="text-sm">Please try refreshing the page</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                            <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium">1</span> to <span class="font-medium">10</span> of <span class="font-medium"><?php echo $month_count; ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left h-5 w-5"></i>
                                    </a>
                                    <a href="#" aria-current="page" class="z-10 bg-indigo-50 border-indigo-500 text-indigo-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        1
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right h-5 w-5"></i>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Sale Modal -->
    <div id="addSaleModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <form method="POST" action="" id="addSaleForm">
                    <input type="hidden" name="action" value="add_sale">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                        Create New Sale
                                    </h3>
                                    <button type="button" onclick="closeAddSaleModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                        <i class="fas fa-times text-xl"></i>
                                    </button>
                                </div>

                                <div class="mt-5 space-y-6">
                                    <!-- Payment Selection Section -->
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <h4 class="text-sm font-medium text-blue-900 mb-3">
                                            <i class="fas fa-credit-card mr-2"></i>Link to Existing Payment (Optional)
                                        </h4>
                                        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                            <div>
                                                <label for="payment_id" class="block text-sm font-medium text-gray-700">Select Payment</label>
                                                <select id="payment_id" name="payment_id" onchange="loadPaymentInfo()" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md border">
                                                    <option value="">Choose existing payment...</option>
                                                    <?php
                                                    try {
                                                        $payments_query = "
                                                            SELECT p.id, p.transaction_id, p.amount, p.payment_method, u.customer_name
                                                            FROM payments p
                                                            LEFT JOIN customers u ON p.customer_id = u.id
                                                            WHERE p.status = 'completed'
                                                            ORDER BY p.payment_date DESC
                                                            LIMIT 50
                                                        ";
                                                        $payments_result = $pdo->query($payments_query);
                                                        $payments = $payments_result->fetchAll();
                                                        foreach ($payments as $payment) {
                                                            $method_display = [
                                                                'shilling_somali' => 'Shilling Somali',
                                                                'evc' => 'EVC',
                                                                'bank_transfer' => 'Bank Transfer',
                                                                'cash_dollar' => 'Cash Dollar'
                                                            ];
                                                            $method_text = $method_display[$payment['payment_method']] ?? ucfirst(str_replace('_', ' ', $payment['payment_method']));
                                                            echo "<option value='{$payment['id']}'>{$payment['transaction_id']} - {$payment['customer_name']} - \${$payment['amount']} ({$method_text})</option>";
                                                        }
                                                    } catch (Exception $e) {
                                                        echo "<option value=''>Error loading payments</option>";
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Payment Info</label>
                                                <div id="paymentInfo" class="mt-1 p-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 min-h-[38px] flex items-center">
                                                    Select a payment to auto-fill customer and payment method
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                        <!-- Customer Selection -->
                                        <div class="sm:col-span-3">
                                            <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer *</label>
                                            <select id="customer_id" name="customer_id" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md border">
                                                <option value="">Select customer</option>
                                                <?php
                                                try {
                                                    $customers_query = "SELECT id, customer_name, email FROM customers ORDER BY customer_name";
                                                    $customers_result = $pdo->query($customers_query);
                                                    $customers = $customers_result->fetchAll();
                                                    foreach ($customers as $customer) {
                                                        echo "<option value='{$customer['id']}'>{$customer['customer_name']} ({$customer['email']})</option>";
                                                    }
                                                } catch (Exception $e) {
                                                    echo "<option value=''>Error loading customers</option>";
                                                }
                                                ?>
                                            </select>
                                        </div>

                                        <!-- Sale Date -->
                                        <div class="sm:col-span-2">
                                            <label for="sale_date" class="block text-sm font-medium text-gray-700">Sale Date *</label>
                                            <input type="date" name="sale_date" id="sale_date" value="<?php echo date('Y-m-d'); ?>" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                                        </div>

                                        <!-- Payment Method -->
                                        <div class="sm:col-span-1">
                                            <label for="payment_method" class="block text-sm font-medium text-gray-700">Payment Method *</label>
                                            <select id="payment_method" name="payment_method" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md border">
                                                <option value="">Select method</option>
                                                <option value="shilling_somali">Shilling Somali</option>
                                                <option value="evc">EVC</option>
                                                <option value="bank_transfer">Bank Transfer</option>
                                                <option value="cash_dollar">Cash Dollar</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Products Section -->
                                    <div class="border-t pt-6">
                                        <div class="flex justify-between items-center mb-4">
                                            <h4 class="text-md font-medium text-gray-900">Sale Items</h4>
                                            <button type="button" onclick="addSaleItem()" class="bg-indigo-600 text-white px-3 py-1 rounded text-sm hover:bg-indigo-700">
                                                <i class="fas fa-plus mr-1"></i> Add Item
                                            </button>
                                        </div>

                                        <div id="saleItemsContainer">
                                            <!-- Sale items will be added here dynamically -->
                                        </div>

                                        <!-- Total Amount Display -->
                                        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                                            <div class="flex justify-between items-center">
                                                <span class="text-lg font-medium text-gray-900">Total Amount:</span>
                                                <span id="totalAmount" class="text-xl font-bold text-indigo-600">$0.00</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Notes -->
                                    <div class="sm:col-span-6">
                                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                                        <textarea id="notes" name="notes" rows="3" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="Additional notes about this sale..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Create Sale
                        </button>
                        <button type="button" onclick="closeAddSaleModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Sale Details Modal -->
    <div id="viewSaleModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="view-modal-title">
                                    Sale Details
                                </h3>
                                <button type="button" onclick="closeViewSaleModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>

                            <div class="mt-5" id="saleDetailsContent">
                                <!-- Sale details will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="printFromModal()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <i class="fas fa-print mr-2"></i>Print Receipt
                    </button>
                    <button type="button" onclick="closeViewSaleModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        // Available products data (loaded from PHP)
        const availableProducts = <?php
        try {
            $products_query = "SELECT id, product, price, stock, category FROM products WHERE status IN ('In Stock', 'Low Stock') AND stock > 0 ORDER BY product";
            $products_result = $pdo->query($products_query);
            $products_data = $products_result->fetchAll();
            echo json_encode($products_data);
        } catch (Exception $e) {
            echo '[]';
        }
        ?>;

        let saleItemCounter = 0;

        // Load payment information when payment is selected
        function loadPaymentInfo() {
            const paymentSelect = document.getElementById('payment_id');
            const paymentInfo = document.getElementById('paymentInfo');
            const customerSelect = document.getElementById('customer_id');
            const paymentMethodSelect = document.getElementById('payment_method');

            if (!paymentSelect.value) {
                paymentInfo.innerHTML = 'Select a payment to auto-fill customer and payment method';
                paymentInfo.className = 'mt-1 p-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 min-h-[38px] flex items-center';
                return;
            }

            // Show loading state
            paymentInfo.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading payment info...';
            paymentInfo.className = 'mt-1 p-2 bg-white border border-gray-300 rounded-md text-sm text-blue-600 min-h-[38px] flex items-center';

            // Fetch payment information
            fetch(`?action=get_payment_info&payment_id=${paymentSelect.value}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const payment = data.payment;

                        // Update payment info display
                        const methodDisplay = {
                            'shilling_somali': 'Shilling Somali',
                            'evc': 'EVC Plus', // Assuming EVC might mean EVC Plus
                            'bank_transfer': 'Bank Transfer',
                            'cash_dollar': 'Cash Dollar'
                        };
                        const methodText = methodDisplay[payment.payment_method] || payment.payment_method;

                        paymentInfo.innerHTML = `
                            <div class="space-y-1">
                                <div><strong>Customer:</strong> ${payment.customer_name || 'N/A'}</div>
                                <div><strong>Amount:</strong> $${parseFloat(payment.amount).toFixed(2)}</div>
                                <div><strong>Method:</strong> ${methodText}</div>
                                <div><strong>Transaction:</strong> ${payment.transaction_id}</div>
                            </div>
                        `;
                        paymentInfo.className = 'mt-1 p-2 bg-green-50 border border-green-300 rounded-md text-sm text-green-800 min-h-[38px]';

                        // Auto-fill customer and payment method
                        customerSelect.value = payment.customer_id;
                        paymentMethodSelect.value = payment.payment_method;

                        // Highlight the auto-filled fields
                        customerSelect.className = customerSelect.className.replace('border-gray-300', 'border-green-300 bg-green-50');
                        paymentMethodSelect.className = paymentMethodSelect.className.replace('border-gray-300', 'border-green-300 bg-green-50');

                        // Remove highlight after 3 seconds
                        setTimeout(() => {
                            customerSelect.className = customerSelect.className.replace('border-green-300 bg-green-50', 'border-gray-300');
                            paymentMethodSelect.className = paymentMethodSelect.className.replace('border-green-300 bg-green-50', 'border-gray-300');
                        }, 3000);

                    } else {
                        paymentInfo.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${data.message}`;
                        paymentInfo.className = 'mt-1 p-2 bg-red-50 border border-red-300 rounded-md text-sm text-red-600 min-h-[38px] flex items-center';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    paymentInfo.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Error loading payment information';
                    paymentInfo.className = 'mt-1 p-2 bg-red-50 border border-red-300 rounded-md text-sm text-red-600 min-h-[38px] flex items-center';
                });
        }

        // Modal functions
        function openAddSaleModal() {
            document.getElementById('addSaleModal').classList.remove('hidden');
            // Add first sale item automatically
            addSaleItem();
        }

        function closeAddSaleModal() {
            document.getElementById('addSaleModal').classList.add('hidden');
            // Reset form
            document.getElementById('addSaleForm').reset();
            document.getElementById('saleItemsContainer').innerHTML = '';
            document.getElementById('totalAmount').textContent = '$0.00';
            saleItemCounter = 0;

            // Reset payment info
            document.getElementById('paymentInfo').innerHTML = 'Select a payment to auto-fill customer and payment method';
            document.getElementById('paymentInfo').className = 'mt-1 p-2 bg-white border border-gray-300 rounded-md text-sm text-gray-500 min-h-[38px] flex items-center';
        }

        // Add sale item row
        function addSaleItem() {
            saleItemCounter++;
            const container = document.getElementById('saleItemsContainer');

            const itemHtml = `
                <div class="sale-item border border-gray-200 rounded-lg p-4 mb-4" id="saleItem${saleItemCounter}">
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-12 items-end">
                        <div class="sm:col-span-5">
                            <label class="block text-sm font-medium text-gray-700">Product *</label>
                            <select name="sale_items[${saleItemCounter}][product_id]" onchange="updateProductPrice(${saleItemCounter})" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                                <option value="">Select product</option>
                                ${availableProducts.map(product =>
                                    `<option value="${product.id}" data-price="${product.price}" data-stock="${product.stock}">${product.product} - $${parseFloat(product.price).toFixed(2)} (Stock: ${product.stock})</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="sm:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Quantity *</label>
                            <input type="number" name="sale_items[${saleItemCounter}][quantity]" min="1" value="1" onchange="updateItemTotal(${saleItemCounter})" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                        </div>
                        <div class="sm:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Unit Price *</label>
                            <input type="number" name="sale_items[${saleItemCounter}][unit_price]" step="0.01" min="0" onchange="updateItemTotal(${saleItemCounter})" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                        </div>
                        <div class="sm:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Subtotal</label>
                            <input type="text" readonly class="mt-1 block w-full bg-gray-100 border-gray-300 rounded-md p-2 border" id="subtotal${saleItemCounter}" value="$0.00">
                        </div>
                        <div class="sm:col-span-1">
                            <button type="button" onclick="removeSaleItem(${saleItemCounter})" class="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', itemHtml);
        }

        // Remove sale item
        function removeSaleItem(itemId) {
            const item = document.getElementById(`saleItem${itemId}`);
            if (item) {
                item.remove();
                updateTotalAmount();
            }
        }

        // Update product price when product is selected
        function updateProductPrice(itemId) {
            const select = document.querySelector(`select[name="sale_items[${itemId}][product_id]"]`);
            const priceInput = document.querySelector(`input[name="sale_items[${itemId}][unit_price]"]`);
            const quantityInput = document.querySelector(`input[name="sale_items[${itemId}][quantity]"]`);

            if (select.value) {
                const selectedOption = select.options[select.selectedIndex];
                const price = selectedOption.getAttribute('data-price');
                const stock = selectedOption.getAttribute('data-stock');

                priceInput.value = parseFloat(price).toFixed(2);
                quantityInput.max = stock;

                updateItemTotal(itemId);
            } else {
                priceInput.value = '';
                quantityInput.max = '';
                updateItemTotal(itemId);
            }
        }

        // Update item subtotal
        function updateItemTotal(itemId) {
            const quantityInput = document.querySelector(`input[name="sale_items[${itemId}][quantity]"]`);
            const priceInput = document.querySelector(`input[name="sale_items[${itemId}][unit_price]"]`);
            const subtotalInput = document.getElementById(`subtotal${itemId}`);

            const quantity = parseFloat(quantityInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;
            const subtotal = quantity * price;

            subtotalInput.value = '$' + subtotal.toFixed(2);
            updateTotalAmount();
        }

        // Update total amount
        function updateTotalAmount() {
            let total = 0;
            const subtotalInputs = document.querySelectorAll('[id^="subtotal"]');

            subtotalInputs.forEach(input => {
                const value = input.value.replace('$', '');
                total += parseFloat(value) || 0;
            });

            document.getElementById('totalAmount').textContent = '$' + total.toFixed(2);
        }

        // View sale details
        function viewSale(saleId) {
            fetch(`get_sale_details.php?id=${saleId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSaleDetailsModal(data.sale, data.items);
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading sale details');
                });
        }

        // Print receipt
        function printReceipt(saleId) {
            fetch(`get_sale_details.php?id=${saleId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        generateReceipt(data.sale, data.items);
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading sale details for receipt');
                });
        }

        // Export sales
        function exportSales() {
            alert('Export sales - Feature coming soon!');
        }

        // Print sales list
        function printSales() {
            window.print();
        }

        // Form validation before submit
        document.getElementById('addSaleForm').addEventListener('submit', function(e) {
            const saleItems = document.querySelectorAll('.sale-item');
            if (saleItems.length === 0) {
                e.preventDefault();
                alert('Please add at least one sale item.');
                return false;
            }

            // Check if all items have valid data
            let hasValidItems = false;
            saleItems.forEach(item => {
                const productSelect = item.querySelector('select[name*="[product_id]"]');
                const quantityInput = item.querySelector('input[name*="[quantity]"]');
                const priceInput = item.querySelector('input[name*="[unit_price]"]');

                if (productSelect.value && quantityInput.value && priceInput.value) {
                    hasValidItems = true;
                }
            });

            if (!hasValidItems) {
                e.preventDefault();
                alert('Please ensure all sale items have valid product, quantity, and price.');
                return false;
            }
        });

        // Variables to store current sale data for printing
        let currentSaleData = null;
        let currentSaleItems = null;

        // Show sale details modal
        function showSaleDetailsModal(sale, items) {
            currentSaleData = sale;
            currentSaleItems = items;

            const formatCurrency = (amount) => '$' + parseFloat(amount).toFixed(2);
            const formatDate = (dateString) => new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            const paymentMethodMap = {
                'shilling_somali': 'Shilling Somali',
                'evc': 'EVC',
                'bank_transfer': 'Bank Transfer',
                'cash_dollar': 'Cash Dollar'
            };

            const content = `
                <div class="space-y-6">
                    <!-- Sale Information -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Sale Information</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <span class="text-sm font-medium text-gray-500">Sale ID:</span>
                                <p class="text-sm text-gray-900">#${sale.id}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Sale Date:</span>
                                <p class="text-sm text-gray-900">${formatDate(sale.sale_date)}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Total Amount:</span>
                                <p class="text-lg font-bold text-green-600">${formatCurrency(sale.total_amount)}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Payment Method:</span>
                                <p class="text-sm text-gray-900">${paymentMethodMap[sale.payment_method] || sale.payment_method}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Payment Status:</span>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${sale.payment_status === 'Paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">${sale.payment_status || 'Pending'}</span>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Created By:</span>
                                <p class="text-sm text-gray-900">${sale.created_by || 'N/A'}</p>
                            </div>
                        </div>
                        ${sale.notes ? `
                        <div class="mt-4">
                            <span class="text-sm font-medium text-gray-500">Notes:</span>
                            <p class="text-sm text-gray-900 mt-1">${sale.notes}</p>
                        </div>
                        ` : ''}
                    </div>

                    <!-- Customer Information -->
                    <div class="bg-blue-50 rounded-lg p-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <span class="text-sm font-medium text-gray-500">Name:</span>
                                <p class="text-sm text-gray-900">${sale.customer.name || 'N/A'}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Email:</span>
                                <p class="text-sm text-gray-900">${sale.customer.email || 'N/A'}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Phone:</span>
                                <p class="text-sm text-gray-900">${sale.customer.phone || 'N/A'}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Sale Items -->
                    <div class="bg-white rounded-lg">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Sale Items</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    ${items.map(item => `
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.product_name}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.product_category || 'N/A'}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.quantity}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatCurrency(item.unit_price)}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${formatCurrency(item.subtotal)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="bg-gray-50">
                                        <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">Total:</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600">${formatCurrency(sale.total_amount)}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('saleDetailsContent').innerHTML = content;
            document.getElementById('viewSaleModal').classList.remove('hidden');
        }

        // Close sale details modal
        function closeViewSaleModal() {
            document.getElementById('viewSaleModal').classList.add('hidden');
            currentSaleData = null;
            currentSaleItems = null;
        }

        // Print from modal
        function printFromModal() {
            if (currentSaleData && currentSaleItems) {
                generateReceipt(currentSaleData, currentSaleItems);
            }
        }

        // Generate receipt
        function generateReceipt(sale, items) {
            const formatCurrency = (amount) => '$' + parseFloat(amount).toFixed(2);
            const formatDate = (dateString) => new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            const paymentMethodMap = {
                'shilling_somali': 'Shilling Somali',
                'evc': 'EVC',
                'bank_transfer': 'Bank Transfer',
                'cash_dollar': 'Cash Dollar'
            };

            const receiptWindow = window.open('', '_blank');
            const receiptHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Receipt - Sale #${sale.id}</title>
                    <style>
                        body {
                            font-family: 'Courier New', monospace;
                            max-width: 80mm;
                            margin: 0 auto;
                            padding: 10mm;
                            font-size: 12px;
                            line-height: 1.4;
                        }
                        .receipt-header {
                            text-align: center;
                            margin-bottom: 20px;
                            border-bottom: 2px solid #000;
                            padding-bottom: 10px;
                        }
                        .business-name {
                            font-size: 18px;
                            font-weight: bold;
                            margin-bottom: 5px;
                        }
                        .business-info {
                            font-size: 10px;
                            margin-bottom: 2px;
                        }
                        .receipt-title {
                            font-size: 16px;
                            font-weight: bold;
                            margin: 15px 0;
                            text-align: center;
                        }
                        .info-section {
                            margin-bottom: 15px;
                        }
                        .info-row {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 3px;
                        }
                        .items-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 15px;
                        }
                        .items-table th,
                        .items-table td {
                            text-align: left;
                            padding: 3px 2px;
                            border-bottom: 1px dashed #ccc;
                        }
                        .items-table th {
                            border-bottom: 1px solid #000;
                            font-weight: bold;
                        }
                        .total-section {
                            border-top: 2px solid #000;
                            padding-top: 10px;
                            margin-top: 15px;
                        }
                        .total-row {
                            display: flex;
                            justify-content: space-between;
                            font-weight: bold;
                            font-size: 14px;
                            margin-bottom: 5px;
                        }
                        .footer {
                            text-align: center;
                            margin-top: 20px;
                            padding-top: 15px;
                            border-top: 1px dashed #000;
                            font-size: 10px;
                        }
                        .stamp-area {
                            margin-top: 20px;
                            padding: 15px;
                            border: 2px dashed #000;
                            text-align: center;
                            font-size: 10px;
                        }
                        @media print {
                            body { margin: 0; padding: 5mm; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="receipt-header">
                        <div class="business-name">MEAT MANAGEMENT SYSTEM</div>
                        <div class="business-info">Fresh Quality Meat Supply</div>
                        <div class="business-info">Tel: +252-XX-XXXXXXX</div>
                        <div class="business-info">Email: <EMAIL></div>
                        <div class="business-info">Address: Your Business Address</div>
                    </div>

                    <div class="receipt-title">SALES RECEIPT</div>

                    <div class="info-section">
                        <div class="info-row">
                            <span>Receipt #:</span>
                            <span>#${sale.id}</span>
                        </div>
                        <div class="info-row">
                            <span>Date:</span>
                            <span>${formatDate(sale.sale_date)}</span>
                        </div>
                        <div class="info-row">
                            <span>Time:</span>
                            <span>${new Date().toLocaleTimeString()}</span>
                        </div>
                        <div class="info-row">
                            <span>Served by:</span>
                            <span>${sale.created_by || 'Staff'}</span>
                        </div>
                    </div>

                    <div class="info-section">
                        <strong>Customer Information:</strong>
                        <div class="info-row">
                            <span>Name:</span>
                            <span>${sale.customer.name || 'Walk-in Customer'}</span>
                        </div>
                        ${sale.customer.phone ? `
                        <div class="info-row">
                            <span>Phone:</span>
                            <span>${sale.customer.phone}</span>
                        </div>
                        ` : ''}
                    </div>

                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Qty</th>
                                <th>Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${items.map(item => `
                                <tr>
                                    <td>${item.product_name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${formatCurrency(item.unit_price)}</td>
                                    <td>${formatCurrency(item.subtotal)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <div class="total-section">
                        <div class="total-row">
                            <span>TOTAL AMOUNT:</span>
                            <span>${formatCurrency(sale.total_amount)}</span>
                        </div>
                        <div class="info-row">
                            <span>Payment Method:</span>
                            <span>${paymentMethodMap[sale.payment_method] || sale.payment_method}</span>
                        </div>
                        <div class="info-row">
                            <span>Payment Status:</span>
                            <span>${sale.payment_status || 'Pending'}</span>
                        </div>
                    </div>

                    ${sale.notes ? `
                    <div class="info-section">
                        <strong>Notes:</strong>
                        <div style="margin-top: 5px;">${sale.notes}</div>
                    </div>
                    ` : ''}

                    <div class="stamp-area">
                        <div>OFFICIAL STAMP & SIGNATURE</div>
                        <div style="margin-top: 30px;">
                            <div>_________________________</div>
                            <div>Authorized Signature</div>
                        </div>
                    </div>

                    <div class="footer">
                        <div>Thank you for your business!</div>
                        <div>Visit us again soon</div>
                        <div style="margin-top: 10px;">
                            <small>Generated on ${new Date().toLocaleString()}</small>
                        </div>
                    </div>

                    <div class="no-print" style="text-align: center; margin-top: 20px;">
                        <button onclick="window.print()" style="padding: 10px 20px; background: #4F46E5; color: white; border: none; border-radius: 5px; cursor: pointer;">Print Receipt</button>
                        <button onclick="window.close()" style="padding: 10px 20px; background: #6B7280; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">Close</button>
                    </div>
                </body>
                </html>
            `;

            receiptWindow.document.write(receiptHTML);
            receiptWindow.document.close();
            receiptWindow.focus();
        }
    </script>
</body>
</html>
