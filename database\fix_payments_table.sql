-- Fix payments table to work with customers table and correct payment methods
-- Run this SQL in your MySQL database to fix the payments table

-- Step 1: Find and drop ALL foreign key constraints on customer_id column
-- First, let's see what constraints exist
SELECT
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_NAME = 'payments'
AND TABLE_SCHEMA = 'meat_sys'
AND COLUMN_NAME = 'customer_id'
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- Drop the foreign key constraint (replace 'constraint_name' with actual name from above query)
-- Common constraint names to try:
-- ALTER TABLE `payments` DROP FOREIGN KEY `payments_ibfk_1`;
-- ALTER TABLE `payments` DROP FOREIGN KEY `payments_ibfk_2`;
-- ALTER TABLE `payments` DROP FOREIGN KEY `fk_payments_customer_id`;

-- If you're not sure of the constraint name, you can drop all foreign keys:
-- ALTER TABLE `payments` DROP FOREIGN KEY `payments_ibfk_1`;
-- ALTER TABLE `payments` DROP FOREIGN KEY `payments_ibfk_2`;

-- Step 2: Modify customer_id column to allow NULL (in case of constraint issues)
ALTER TABLE `payments` MODIFY COLUMN `customer_id` INT(11) NULL;

-- Step 3: Add new foreign key constraint to reference customers table
ALTER TABLE `payments`
ADD CONSTRAINT `fk_payments_customer_new`
FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Step 4: Verify the changes
DESCRIBE `payments`;

-- Step 5: Show the new foreign key constraints
SELECT
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_NAME = 'payments'
AND TABLE_SCHEMA = 'meat_sys'
AND REFERENCED_TABLE_NAME IS NOT NULL;
