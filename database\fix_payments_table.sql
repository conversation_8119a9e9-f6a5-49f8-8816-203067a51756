-- Fix payments table to work with customers table and correct payment methods
-- Run this SQL in your MySQL database to fix the payments table
-- This version avoids using information_schema due to permission issues

-- Step 1: Try to drop common foreign key constraint names
-- Run these one by one, ignore errors for constraints that don't exist

-- Try dropping the most common constraint names:
ALTER TABLE `payments` DROP FOREIGN KEY `payments_ibfk_1`;

-- If the above fails, try this one:
-- ALTER TABLE `payments` DROP FOREIGN KEY `payments_ibfk_2`;

-- If both fail, try this one:
-- ALTER TABLE `payments` DROP FOREIGN KEY `fk_payments_customer`;

-- Step 2: Modify customer_id column to allow NULL (in case of constraint issues)
ALTER TABLE `payments` MODIFY COLUMN `customer_id` INT(11) NULL;

-- Step 3: Add new foreign key constraint to reference customers table
ALTER TABLE `payments`
ADD CONSTRAINT `fk_payments_customer_new`
FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Step 4: Verify the changes
DESCRIBE `payments`;
