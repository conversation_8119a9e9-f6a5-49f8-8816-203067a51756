-- Fix payments table to work with customers table and correct payment methods
-- Run this SQL in your MySQL database to fix the payments table

-- First, drop the foreign key constraint that references users table
ALTER TABLE `payments` DROP FOREIGN KEY `payments_ibfk_2`;

-- Modify the payment_method enum to include the correct values
ALTER TABLE `payments` MODIFY COLUMN `payment_method` 
ENUM('shilling_somali','evc','bank_transfer','cash_dollar','credit_card','paypal','cash','mobile_money') NOT NULL;

-- Add foreign key constraint to reference customers table instead of users
ALTER TABLE `payments` 
ADD CONSTRAINT `fk_payments_customer` 
FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL;

-- Optional: Update existing payment methods if any exist
-- UPDATE `payments` SET `payment_method` = 'bank_transfer' WHERE `payment_method` = 'bank_transfer';
-- UPDATE `payments` SET `payment_method` = 'cash_dollar' WHERE `payment_method` = 'cash';

-- Show the updated table structure
DESCRIBE `payments`;
