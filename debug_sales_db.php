<?php
/**
 * Debug script to check sales table structure and data
 * Run this script to diagnose sales insertion issues
 */

require_once 'database/conn.php';

echo "<h2>Database Diagnostics for Sales</h2>";

try {
    // Check if sales table exists
    echo "<h3>1. Checking if sales table exists...</h3>";
    $result = $pdo->query("SHOW TABLES LIKE 'sales'");
    if ($result->rowCount() > 0) {
        echo "✅ Sales table exists<br>";
    } else {
        echo "❌ Sales table does NOT exist<br>";
        echo "<strong>Solution:</strong> Run the database/create_sales_tables.sql script<br>";
        exit;
    }

    // Check sales table structure
    echo "<h3>2. Sales table structure:</h3>";
    $result = $pdo->query("DESCRIBE sales");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table><br>";

    // Check payment_method column type
    echo "<h3>3. Payment method column details:</h3>";
    $result = $pdo->query("SHOW COLUMNS FROM sales LIKE 'payment_method'");
    $column = $result->fetch();
    echo "Current type: <strong>{$column['Type']}</strong><br>";
    
    // Check if our payment methods are supported
    $our_methods = ['shilling_somali', 'evc', 'bank_transfer', 'cash_dollar'];
    echo "Our payment methods: " . implode(', ', $our_methods) . "<br>";
    
    if (strpos($column['Type'], 'enum') !== false) {
        echo "⚠️ Column is ENUM type - may not support our custom payment methods<br>";
        echo "<strong>Recommendation:</strong> Change to VARCHAR to support custom payment methods<br>";
    } else {
        echo "✅ Column type supports custom payment methods<br>";
    }

    // Check foreign key constraints (alternative method)
    echo "<h3>4. Foreign key constraints on sales table:</h3>";
    try {
        $result = $pdo->query("SHOW CREATE TABLE `sales`");
        $create_table = $result->fetch();
        $create_sql = $create_table['Create Table'];
        
        echo "<strong>Table creation SQL:</strong><br>";
        echo "<pre style='background-color: #f5f5f5; padding: 10px; overflow-x: auto;'>";
        echo htmlspecialchars($create_sql);
        echo "</pre>";
        
        // Check for foreign key references
        if (strpos($create_sql, 'REFERENCES `users`') !== false) {
            echo "<div style='background-color: #ffcccc; padding: 10px; margin: 10px 0;'>";
            echo "⚠️ PROBLEM FOUND: The sales table has foreign keys that reference the 'users' table.<br>";
            echo "This needs to be changed for customer_id to reference the 'customers' table instead.";
            echo "</div>";
        } elseif (strpos($create_sql, 'REFERENCES `customers`') !== false) {
            echo "<div style='background-color: #ccffcc; padding: 10px; margin: 10px 0;'>";
            echo "✅ GOOD: The sales table correctly references the 'customers' table.";
            echo "</div>";
        } else {
            echo "<div style='background-color: #ffffcc; padding: 10px; margin: 10px 0;'>";
            echo "ℹ️ INFO: No foreign key constraints found.";
            echo "</div>";
        }
        
    } catch (PDOException $e) {
        echo "Could not check foreign key constraints: " . $e->getMessage() . "<br>";
    }

    // Check if customers table exists and has data
    echo "<h3>5. Checking customers table...</h3>";
    $result = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($result->rowCount() > 0) {
        echo "✅ Customers table exists<br>";
        
        // Count customers
        $result = $pdo->query("SELECT COUNT(*) as count FROM customers");
        $count = $result->fetch()['count'];
        echo "Number of customers: $count<br>";
        
        if ($count > 0) {
            echo "Sample customers:<br>";
            $result = $pdo->query("SELECT id, customer_name, email, status FROM customers LIMIT 5");
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Status</th></tr>";
            while ($row = $result->fetch()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['customer_name']}</td>";
                echo "<td>{$row['email']}</td>";
                echo "<td>{$row['status']}</td>";
                echo "</tr>";
            }
            echo "</table><br>";
        }
    } else {
        echo "❌ Customers table does NOT exist<br>";
        echo "<strong>Solution:</strong> Create customers table first<br>";
    }

    // Check if sale_items table exists
    echo "<h3>6. Checking sale_items table...</h3>";
    $result = $pdo->query("SHOW TABLES LIKE 'sale_items'");
    if ($result->rowCount() > 0) {
        echo "✅ Sale_items table exists<br>";
    } else {
        echo "❌ Sale_items table does NOT exist<br>";
        echo "<strong>Solution:</strong> Run the database/create_sales_tables.sql script<br>";
    }

    // Check if products table has data
    echo "<h3>7. Checking products table...</h3>";
    $result = $pdo->query("SELECT COUNT(*) as count FROM products WHERE stock > 0");
    $count = $result->fetch()['count'];
    echo "Products with stock: $count<br>";
    
    if ($count == 0) {
        echo "⚠️ No products with stock available for sales<br>";
        echo "<strong>Solution:</strong> Add products with stock > 0<br>";
    }

    // Test sales insertion with sample data
    echo "<h3>8. Testing sales insertion...</h3>";
    
    // Get first customer ID
    $result = $pdo->query("SELECT id FROM customers WHERE status = 'Active' LIMIT 1");
    if ($result->rowCount() > 0) {
        $customer_id = $result->fetch()['id'];
        echo "Using customer ID: $customer_id<br>";
        
        // Get first user ID for created_by
        $result = $pdo->query("SELECT id FROM users LIMIT 1");
        $created_by = $result->rowCount() > 0 ? $result->fetch()['id'] : 1;
        echo "Using created_by user ID: $created_by<br>";
        
        // Try to insert a test sale
        try {
            // Disable foreign key checks temporarily
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            $stmt = $pdo->prepare("
                INSERT INTO sales (customer_id, sale_date, total_amount, payment_method, payment_status, notes, created_by, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $result = $stmt->execute([
                $customer_id,
                date('Y-m-d'),
                25.00,
                'bank_transfer',
                'Paid',
                'Test sale',
                $created_by
            ]);
            
            // Re-enable foreign key checks
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            if ($result) {
                echo "✅ Test sale inserted successfully<br>";
                
                // Clean up test sale
                $pdo->prepare("DELETE FROM sales WHERE notes = 'Test sale'")->execute();
                echo "Test sale cleaned up<br>";
            } else {
                echo "❌ Test sale insertion failed<br>";
            }
            
        } catch (PDOException $e) {
            echo "❌ Test sale insertion error: " . $e->getMessage() . "<br>";
            echo "Error code: " . $e->getCode() . "<br>";
        }
    } else {
        echo "❌ No active customers found for testing<br>";
    }

} catch (PDOException $e) {
    echo "Database connection error: " . $e->getMessage();
}
?>

<style>
table { margin: 10px 0; }
th, td { padding: 5px 10px; text-align: left; }
th { background-color: #f0f0f0; }
</style>
